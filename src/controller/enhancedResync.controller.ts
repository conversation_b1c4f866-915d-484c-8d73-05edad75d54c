/**
 * Enhanced Resync Controller
 * 
 * This controller provides enhanced calendar resync endpoints with:
 * - Reliable reschedule detection
 * - Separation of syncable, not_syncable, and rescheduleEvents
 * - Support for both detached series events and timing-based detection
 * - No assumptions or risky operations
 */

import express from "express";
import { EnhancedResyncService } from "../services/enhancedResync.service";
import { Response } from "../util/response";

export class EnhancedResyncController {
  
  /**
   * Enhanced resync endpoint with reschedule detection
   * GET /api/v1/therapist/calendar/enhanced-resync
   */
  static async enhancedResync(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      console.log(`🔄 Enhanced resync requested for therapist ${req.therapist._id}`);
      
      const maxResults = Number(req.query.maxResults) || 200;
      
      const resyncResult = await EnhancedResyncService.resyncCalendarEventsWithRescheduleDetection(
        req.therapist._id,
        maxResults
      );

      if (!resyncResult) {
        return res.status(400).json({
          success: false,
          message: "Unable to resync calendar events.",
        });
      }

      // Update sync date if no syncable events found
      if (resyncResult.syncable.length === 0) {
        req.therapist.syncDate = new Date();
        await req.therapist.save();
      }

      // Log the results
      console.log(`✅ Enhanced resync completed for therapist ${req.therapist._id}:`, {
        syncable: resyncResult.syncable.length,
        not_syncable: resyncResult.not_syncable.length,
        rescheduleEvents: resyncResult.rescheduleEvents.length
      });

      res.json(resyncResult);
      
      // Log API response if apiLog exists
      if (req.apiLog) {
        req.apiLog.response = JSON.stringify({ resyncResult });
        await req.apiLog.save();
      }

    } catch (error: any) {
      console.error(`💥 Error in enhanced resync:`, error.message);
      next(error);
    }
  }

  /**
   * Enhanced resync endpoint for specific therapist (admin use)
   * GET /api/v1/therapist/calendar/enhanced-resync/:therapistId
   */
  static async enhancedResyncForTherapist(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.therapistId;
      const maxResults = Number(req.query.maxResults) || 200;
      
      console.log(`🔄 Enhanced resync requested for therapist ${therapistId} by admin`);
      
      const resyncResult = await EnhancedResyncService.resyncCalendarEventsWithRescheduleDetection(
        therapistId,
        maxResults
      );

      if (!resyncResult) {
        return res.status(400).json({
          success: false,
          message: "Unable to resync calendar events.",
        });
      }

      // Log the results
      console.log(`✅ Enhanced resync completed for therapist ${therapistId}:`, {
        syncable: resyncResult.syncable.length,
        not_syncable: resyncResult.not_syncable.length,
        rescheduleEvents: resyncResult.rescheduleEvents.length
      });

      res.json(resyncResult);

    } catch (error: any) {
      console.error(`💥 Error in enhanced resync for therapist ${req.params.therapistId}:`, error.message);
      next(error);
    }
  }

  /**
   * Get reschedule events only
   * GET /api/v1/therapist/calendar/reschedule-events
   */
  static async getRescheduleEventsOnly(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      console.log(`🔍 Reschedule events requested for therapist ${req.therapist._id}`);
      
      const maxResults = Number(req.query.maxResults) || 200;
      
      const resyncResult = await EnhancedResyncService.resyncCalendarEventsWithRescheduleDetection(
        req.therapist._id,
        maxResults
      );

      if (!resyncResult) {
        return res.status(400).json({
          success: false,
          message: "Unable to fetch reschedule events.",
        });
      }

      console.log(`✅ Found ${resyncResult.rescheduleEvents.length} reschedule events for therapist ${req.therapist._id}`);

      res.json({
        success: true,
        rescheduleEvents: resyncResult.rescheduleEvents,
        count: resyncResult.rescheduleEvents.length
      });

    } catch (error: any) {
      console.error(`💥 Error getting reschedule events:`, error.message);
      next(error);
    }
  }

  /**
   * Check calendar sync status with reschedule detection
   * GET /api/v1/therapist/calendar/enhanced-sync-status
   */
  static async checkEnhancedSyncStatus(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const moment = require("moment");
      
      let syncDate = req.therapist.syncDate
        ? moment(req.therapist.syncDate)
        : moment().subtract("1", "month");

      let currentDateMinus7Days = moment().subtract(7, "days");

      let isSyncAvailable: boolean = false;
      let hasRescheduleEvents: boolean = false;

      if (syncDate.isAfter(currentDateMinus7Days)) {
        return res.status(200).send(
          new Response(
            { 
              syncable: isSyncAvailable,
              hasRescheduleEvents: hasRescheduleEvents
            },
            "Calendar status fetched successfully",
            200
          )
        );
      }

      const syncStatus = await EnhancedResyncService.resyncCalendarEventsWithRescheduleDetection(
        req.therapist._id,
        200
      );
      
      if (syncStatus) {
        isSyncAvailable = syncStatus.syncable.length > 0;
        hasRescheduleEvents = syncStatus.rescheduleEvents.length > 0;
      }

      res.status(200).send(
        new Response(
          { 
            syncable: isSyncAvailable,
            hasRescheduleEvents: hasRescheduleEvents,
            syncableCount: syncStatus?.syncable.length || 0,
            rescheduleCount: syncStatus?.rescheduleEvents.length || 0
          },
          "Enhanced calendar status fetched successfully",
          200
        )
      );

    } catch (error: any) {
      console.error(`💥 Error checking enhanced sync status:`, error.message);
      next(error);
    }
  }
}
